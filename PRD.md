# Product Requirements Document (PRD)
## Collie E-Services Platform

### 1. Project Overview

**Project Name:** Collie E-Services Platform  
**API Version:** 1.1.0  
**Project Type:** Service Marketplace Platform  
**Business Domain:** On-demand service booking and management

Collie is a comprehensive e-services marketplace platform that connects service providers with customers, enabling seamless service discovery, booking, and management. The platform facilitates the entire service lifecycle from provider registration to order completion and payment processing.

### 2. Architecture & Technology Stack

#### 2.1 Core Framework
- **Primary Framework:** Quarkus 3.6.0 (Supersonic Subatomic Java Framework)
- **Runtime:** Java 17
- **Build Tool:** Maven
- **Package Type:** Uber JAR

#### 2.2 Database & Persistence
- **Database:** PostgreSQL
- **ORM:** Hibernate ORM with Panache
- **Database Migration:** Liquibase
- **Connection Pooling:** Built-in Quarkus datasource management

#### 2.3 Authentication & Security
- **Authentication Provider:** Firebase Authentication
- **Authorization:** Role-based access control (Users, Providers, Admins)
- **Security Framework:** Quarkus Security with JWT

#### 2.4 Cloud Services & Infrastructure
- **Cloud Platform:** Google Cloud Platform
- **File Storage:** Google Cloud Storage
- **Email Service:** SendGrid API
- **Deployment:** Google App Engine
- **Containerization:** Docker (JVM and Native builds)

#### 2.5 API & Documentation
- **API Style:** RESTful API
- **Documentation:** OpenAPI 3.0 (Swagger UI)
- **Serialization:** Jackson JSON
- **HTTP Framework:** RESTEasy Reactive

#### 2.6 Additional Technologies
- **Template Engine:** jMustache
- **Barcode Generation:** ZXing (2D barcodes for payments)
- **Logging:** SLF4J with custom HTTP logging
- **Development Tools:** P6Spy (SQL logging), Lombok

### 3. Domain Model & Core Entities

#### 3.1 User Management
- **User:** Core user entity with Firebase UID integration
- **Provider:** Service providers extending user functionality
- **Gender:** User demographic information
- **Admin:** Administrative users with elevated privileges

#### 3.2 Service Management
- **Service:** Core service offerings with pricing, categories, and locations
- **ServiceStatus:** Service lifecycle states (active, inactive, etc.)
- **ServiceImage:** Multiple images per service
- **ServiceDelivery:** Delivery options and pricing
- **Category:** Service categorization system
- **Promotion:** Service promotional campaigns
- **Area:** Geographic service areas

#### 3.3 Order Management
- **Order:** Customer service bookings with complete lifecycle
- **OrderStatus:** Order state management (pending, confirmed, completed, etc.)
- **OrderSort:** Sorting options for order listings
- **Comment:** Order communication system
- **Feedback:** Post-service rating and review system
- **ServiceSnapshot:** Immutable service state at order time

#### 3.4 Location & Geography
- **State:** Top-level geographic divisions
- **City:** Service delivery locations
- **Address:** Physical address management

#### 3.5 Payment & Financial
- **Payment:** Payment processing with IBAN integration
- **Company:** Platform company information for payments

#### 3.6 Communication
- **Notification:** In-app notification system
- **NotificationConfig:** Configurable notification templates
- **NotificationEvent:** Event-driven notification triggers

### 4. API Structure & Endpoints

#### 4.1 Public Endpoints (No Authentication)
- **Configuration:** `/config` - Platform configuration and metadata
- **Images:** `/images` - Public image downloads
- **Slugs:** `/slugs` - SEO-friendly URL management
- **Services:** `/services` - Public service browsing
- **Providers:** `/providers` - Public provider profiles
- **Areas:** `/areas` - Geographic area information

#### 4.2 Protected User Endpoints (Authentication Required)
- **User Management:** `/user` - User profile operations
- **User Orders:** `/orders` - Customer order management
- **Notifications:** `/notifications` - User notification system

#### 4.3 Provider Endpoints (Provider Authentication)
- **Provider Management:** `/provider` - Provider profile and business operations
- **Provider Services:** `/provider/services` - Service catalog management
- **Provider Slugs:** `/provider/slugs` - SEO URL management
- **Provider Orders:** `/provider/orders` - Order fulfillment and management

#### 4.4 Administrative Endpoints (Admin Authentication)
- **Admin Operations:** `/admin` - Platform administration and management

### 5. Core Use Cases & Business Workflows

#### 5.1 User Registration & Authentication
- Firebase-based user registration and login
- Automatic user profile creation
- Role-based access control (Customer, Provider, Admin)

#### 5.2 Service Provider Onboarding
- Provider registration and profile setup
- Service catalog creation and management
- Administrative verification process
- Business information and payment details

#### 5.3 Service Discovery & Booking
- Geographic-based service search
- Category and filter-based browsing
- Service detail viewing with images and pricing
- Real-time availability checking
- Order placement with delivery options

#### 5.4 Order Management Lifecycle
1. **Order Creation:** Customer places order with service selection
2. **Order Processing:** Provider receives and confirms order
3. **Service Delivery:** Scheduled service execution
4. **Payment Processing:** 2D barcode generation for bank transfers
5. **Completion:** Service completion and feedback collection

#### 5.5 Communication & Notifications
- Event-driven notification system
- Order status updates
- Provider-customer communication
- Email notifications via SendGrid

#### 5.6 Payment Processing
- IBAN-based payment system
- 2D barcode generation for bank transfers
- Order-specific payment tracking
- Company payment information management

### 6. Key Features

#### 6.1 Multi-tenant Architecture
- Support for multiple service providers
- Isolated provider data and operations
- Centralized platform management

#### 6.2 Geographic Service Coverage
- State and city-based service areas
- Location-aware service discovery
- Delivery zone management

#### 6.3 Rich Media Support
- Multiple image uploads per service
- Google Cloud Storage integration
- Optimized image delivery

#### 6.4 Comprehensive Order Tracking
- Real-time order status updates
- Service snapshot preservation
- Complete order history

#### 6.5 Rating & Review System
- Post-service feedback collection
- Provider rating aggregation
- Quality assurance mechanisms

### 7. Deployment & Operations

#### 7.1 Environment Configuration
- **Development:** Local PostgreSQL with P6Spy logging
- **Production:** Google Cloud SQL with App Engine deployment
- **Configuration:** Environment-based property management

#### 7.2 Monitoring & Logging
- Comprehensive HTTP request/response logging
- Database query monitoring
- Application-level debug logging

#### 7.3 Scalability
- Quarkus native compilation support
- Container-based deployment
- Auto-scaling on Google App Engine

### 8. Security Considerations

- Firebase Authentication integration
- JWT-based API security
- Role-based authorization
- CORS configuration for web clients
- Secure file upload handling

### 9. Integration Points

- **Firebase:** User authentication and management
- **Google Cloud Storage:** File and image storage
- **SendGrid:** Email notification delivery
- **PostgreSQL:** Primary data persistence
- **Google App Engine:** Application hosting

This PRD provides a comprehensive overview of the Collie E-Services Platform, covering its architecture, domain model, API structure, and core business workflows.
